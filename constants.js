/* ------------------------------------------------------------------
 *  constants.js   ── pure values only (no functions)
 * ------------------------------------------------------------------*/

/*──────────────────────────────
  Supported file extensions
──────────────────────────────*/
export const SUPPORTED_AUDIO_EXTENSIONS = ['.mp3', '.wav', '.ogg', '.flac', '.aac'];
export const SUPPORTED_DOC_EXTENSIONS   = ['.txt', '.docx'];

/*──────────────────────────────
  UI helper data
──────────────────────────────*/
export const BREAK_DURATIONS = [
  { label: '0.5s Break', value: 0.5 },
  { label: '1.0s Break', value: 1.0 },
  { label: '2.0s Break', value: 2.0 },
];

/*===================================================================
  GOOGLE CLOUD TEXT-TO-SPEECH
===================================================================*/
export const DEFAULT_G_TTS_VOICE_NAME  = 'en-GB-News-L';
export const G_TTS_SPEECH_SPEED        = 0.75;
export const G_TTS_SAMPLE_RATE_HERTZ   = 44_100;

/* Multi-platform env-var lookup for the Google Gemini API key */
export const GEMINI_API_KEY =
  /* Node / server-side */
  (typeof process !== 'undefined' && process.env && (
    process.env.GEMINI_API_KEY ||
    process.env.VITE_G_TTS_API_KEY ||               // legacy compatibility
    process.env.NEXT_PUBLIC_GEMINI_API_KEY          // Next.js client bundle
  )) ||
  /* Vite / import.meta style */
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_G_TTS_API_KEY) ||
  null;

// Legacy alias for backward compatibility
export const G_TTS_API_KEY = GEMINI_API_KEY;

// Debug logging for Google TTS API key
console.log('Constants: import.meta available:', typeof import.meta !== 'undefined');
console.log('Constants: import.meta.env available:', typeof import.meta !== 'undefined' && !!import.meta.env);
if (typeof import.meta !== 'undefined' && import.meta.env) {
  console.log('Constants: All env vars:', Object.keys(import.meta.env));
  console.log('Constants: VITE_G_TTS_API_KEY from env length:', import.meta.env.VITE_G_TTS_API_KEY?.length || 0);
  console.log('Constants: VITE_G_TTS_API_KEY starts with AIza:', import.meta.env.VITE_G_TTS_API_KEY?.startsWith('AIza') || false);
}
console.log('Constants: G_TTS_API_KEY final length:', G_TTS_API_KEY?.length || 0);
console.log('Constants: G_TTS_API_KEY starts with AIza:', G_TTS_API_KEY?.startsWith('AIza') || false);



/* Extension → Google audioEncoding map */
export const EXTENSION_TO_ENCODING_GOOGLE = {
  '.wav':  'LINEAR16',
  '.mp3':  'MP3',
  '.ogg':  'OGG_OPUS',
  '.flac': 'FLAC',
  '.aac':  'MP3',   // synth as MP3 for widest support
};

/*===================================================================
  MICROSOFT AZURE TEXT-TO-SPEECH
===================================================================*/
export const DEFAULT_MS_TTS_VOICE_NAME = 'en-US-AvaMultilingualNeural';
export const MS_TTS_SPEECH_SPEED       = 'default';        // 'slow'|'medium'|'fast' or %
export const MS_TTS_SAMPLE_RATE_HERTZ  = 'audio-24khz-160kbitrate-mono-mp3';

/* Env-var lookup that works for Node, Next.js (NEXT_PUBLIC_), and Vite (VITE_) */
export const MS_TTS_API_KEY =
  (typeof process !== 'undefined' && process.env && (
    process.env.MS_TTS_API_KEY ||
    process.env.NEXT_PUBLIC_MS_TTS_API_KEY
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MS_TTS_API_KEY) ||
  null;

export const MS_TTS_SERVICE_REGION =
  (typeof process !== 'undefined' && process.env && (
    process.env.MS_TTS_SERVICE_REGION ||
    process.env.NEXT_PUBLIC_MS_TTS_SERVICE_REGION
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MS_TTS_SERVICE_REGION) ||
  null;



/*===================================================================
  AI VOICE CREATOR ENCODINGS - Updated: 2025-06-07 13:19
===================================================================*/
export const AI_VOICE_CREATOR_ENCODINGS = {
  'mp3': {
    extension: '.mp3',
    apiValue: 'MP3',
    mimeType: 'audio/mpeg'
  },
  'wav': {
    extension: '.wav',
    apiValue: 'LINEAR16',
    mimeType: 'audio/wav'
  },
  'ogg': {
    extension: '.ogg',
    apiValue: 'OGG_OPUS',
    mimeType: 'audio/ogg'
  },
  'flac': {
    extension: '.flac',
    apiValue: 'FLAC',
    mimeType: 'audio/flac'
  }
};
